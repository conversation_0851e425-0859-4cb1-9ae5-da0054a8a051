#!/usr/bin/env python3
"""
调试reload警告的脚本
"""
import os
import sys
import subprocess
import tempfile
from pathlib import Path

def create_minimal_test():
    """创建最小测试案例"""
    print("🔍 创建最小测试案例...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建最小的FastAPI应用
        app_content = '''
from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

if __name__ == "__main__":
    # 测试1: 传递app对象（会有警告）
    print("=== 测试1: 传递app对象 ===")
    # uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
    
    # 测试2: 传递字符串（不会有警告）
    print("=== 测试2: 传递字符串 ===")
    uvicorn.run("__main__:app", host="0.0.0.0", port=8000, reload=True)
'''
        
        app_file = temp_path / "test_app.py"
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(app_content)
        
        print(f"📁 临时文件: {app_file}")
        
        # 运行测试
        try:
            print("⏳ 运行测试（3秒后停止）...")
            process = subprocess.Popen(
                [sys.executable, str(app_file)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=temp_dir
            )
            
            import time
            time.sleep(3)
            
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()
            
            print("📝 输出结果:")
            if stdout:
                print("STDOUT:")
                print(stdout)
            if stderr:
                print("STDERR:")
                print(stderr)
                
            # 检查警告
            if "You must pass the application as an import string" in stderr:
                print("❌ 发现警告")
                return False
            else:
                print("✅ 没有警告")
                return True
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def check_current_main():
    """检查当前main.py的内容"""
    print("🔍 检查当前main.py...")
    
    backend_dir = Path(__file__).parent / "backend"
    main_file = backend_dir / "main.py"
    
    if not main_file.exists():
        print("❌ main.py不存在")
        return False
    
    try:
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有问题的代码
        if 'uvicorn.run(app,' in content:
            print("❌ 发现问题: main.py中仍然使用 uvicorn.run(app, ...)")
            print("需要改为: uvicorn.run(\"main:app\", ...)")
            return False
        elif 'uvicorn.run("main:app"' in content:
            print("✅ main.py使用正确的字符串格式")
            return True
        else:
            print("⚠️ main.py中没有找到uvicorn.run调用")
            return True
            
    except Exception as e:
        print(f"❌ 读取main.py失败: {e}")
        return False

def check_run_py():
    """检查run.py的内容"""
    print("🔍 检查run.py...")
    
    backend_dir = Path(__file__).parent / "backend"
    run_file = backend_dir / "run.py"
    
    if not run_file.exists():
        print("❌ run.py不存在")
        return False
    
    try:
        with open(run_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用正确格式
        if '"main:app"' in content:
            print("✅ run.py使用正确的字符串格式")
            return True
        else:
            print("❌ run.py格式可能有问题")
            print("内容:")
            print(content)
            return False
            
    except Exception as e:
        print(f"❌ 读取run.py失败: {e}")
        return False

def test_actual_startup():
    """测试实际启动"""
    print("🔍 测试实际启动...")
    
    backend_dir = Path(__file__).parent / "backend"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print(f"🐍 使用Python: {python_cmd}")
        print("⏳ 测试run.py启动...")
        
        # 启动进程
        process = subprocess.Popen(
            [python_cmd, "run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待3秒
        import time
        time.sleep(3)
        
        # 停止进程
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=2)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        print("📝 启动输出:")
        if stdout:
            print("STDOUT:")
            print(stdout)
        if stderr:
            print("STDERR:")
            print(stderr)
        
        # 检查警告
        warning_text = "You must pass the application as an import string"
        if warning_text in stderr or warning_text in stdout:
            print("❌ 仍然有警告")
            return False
        else:
            print("✅ 没有警告")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主函数"""
    print("🐛 调试reload警告")
    print("=" * 50)
    
    tests = [
        ("检查main.py", check_current_main),
        ("检查run.py", check_run_py),
        ("最小测试案例", create_minimal_test),
        ("实际启动测试", test_actual_startup),
    ]
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}通过")
            else:
                print(f"❌ {test_name}失败")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
        
        print("-" * 30)
    
    print("\n💡 如果仍有警告，请检查:")
    print("1. main.py中的uvicorn.run调用是否使用字符串")
    print("2. 是否有其他地方调用了uvicorn.run(app, ...)")
    print("3. 是否有导入时的副作用")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 调试被中断")
        sys.exit(1)
