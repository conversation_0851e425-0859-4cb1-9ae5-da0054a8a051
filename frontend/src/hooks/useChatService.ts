import { useCallback } from 'react';

const API_BASE_URL = '/api';

interface ChatResponse {
  content: string;
  is_complete: boolean;
  error?: boolean;
}

export const useChatService = () => {
  const sendMessage = useCallback(async (
    message: string,
    onChunk: (chunk: string) => void,
    conversationId: string = 'default'
  ) => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversation_id: conversationId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let chunkBuffer = '';
      let lastUpdateTime = Date.now();

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // 处理可能的多个SSE消息
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个可能不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: ChatResponse = JSON.parse(line.slice(6));

              if (data.error) {
                throw new Error(data.content);
              }

              if (data.content) {
                chunkBuffer += data.content;

                // 批量更新：每50ms或累积一定字符数后更新
                const now = Date.now();
                if (now - lastUpdateTime > 50 || chunkBuffer.length > 10) {
                  onChunk(chunkBuffer);
                  chunkBuffer = '';
                  lastUpdateTime = now;
                }
              }

              if (data.is_complete) {
                // 发送剩余的内容
                if (chunkBuffer) {
                  onChunk(chunkBuffer);
                }
                return;
              }
            } catch (parseError) {
              console.warn('解析SSE数据失败:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }, []);

  const clearConversation = useCallback(async (conversationId: string = 'default') => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat/${conversationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('清除对话失败:', error);
      throw error;
    }
  }, []);

  const sendNonStreamMessage = useCallback(async (
    message: string,
    conversationId: string = 'default'
  ) => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversation_id: conversationId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('发送非流式消息失败:', error);
      throw error;
    }
  }, []);

  return {
    sendMessage,
    clearConversation,
    sendNonStreamMessage,
  };
};
