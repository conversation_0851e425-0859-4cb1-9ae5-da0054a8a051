import React, { useState, useRef, useEffect } from 'react';
import { Layout, Button, Space, Typography, Avatar, theme } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, ClearOutlined, SparklesOutlined } from '@ant-design/icons';
import { Bubble, Sender, Welcome, Prompts } from '@ant-design/x';
import { motion, AnimatePresence } from 'framer-motion';
import { useChatService } from '../hooks/useChatService';
import { Message } from '../types/chat';
import './ChatInterface.css';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageIdCounter = useRef(0);
  const { token } = theme.useToken();

  const { sendMessage, clearConversation } = useChatService();

  // 预设提示词
  const promptItems = [
    {
      key: '1',
      label: '写一首诗',
      description: '创作一首优美的诗歌',
      icon: <SparklesOutlined />,
    },
    {
      key: '2',
      label: '解释量子计算',
      description: '用简单的语言解释复杂概念',
      icon: <SparklesOutlined />,
    },
    {
      key: '3',
      label: '推荐一本书',
      description: '根据兴趣推荐好书',
      icon: <SparklesOutlined />,
    },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 节流滚动，避免频繁滚动导致抖动
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const throttledScrollToBottom = () => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    scrollTimeoutRef.current = setTimeout(() => {
      scrollToBottom();
    }, 100);
  };

  useEffect(() => {
    throttledScrollToBottom();
  }, [messages, isTyping]);

  const handleSend = async (messageText: string) => {
    if (!messageText.trim()) return;

    // 生成唯一ID
    const userMessageId = `user_${++messageIdCounter.current}_${Date.now()}`;
    const assistantMessageId = `assistant_${++messageIdCounter.current}_${Date.now()}`;

    const userMessage: Message = {
      id: userMessageId,
      content: messageText,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      const assistantMessage: Message = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 使用 ref 来跟踪累积的内容，避免闭包问题
      let accumulatedContent = '';

      await sendMessage(messageText, (chunk: string) => {
        accumulatedContent += chunk;
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages.find(msg => msg.id === assistantMessageId);
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content = accumulatedContent; // 直接设置而不是累加
          }
          return newMessages;
        });
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages.find(msg => msg.id === assistantMessageId);
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = '抱歉，发送消息时出现了错误，请稍后重试。';
          lastMessage.error = true;
        }
        return newMessages;
      });
    } finally {
      setIsTyping(false);
    }
  };

  const handleClear = async () => {
    try {
      await clearConversation();
      setMessages([]);
    } catch (error) {
      console.error('清除对话失败:', error);
    }
  };

  const handlePromptClick = (prompt: any) => {
    handleSend(prompt.label);
  };

  // 转换消息格式为 Bubble.List 需要的格式
  const bubbleItems = messages.map((message) => ({
    key: message.id,
    role: message.role,
    content: message.content,
    avatar: message.role === 'user' ?
      <Avatar icon={<UserOutlined />} style={{ backgroundColor: token.colorPrimary }} /> :
      <Avatar icon={<RobotOutlined />} style={{ backgroundColor: token.colorSuccess }} />,
    placement: message.role === 'user' ? 'end' : 'start',
    typing: message.role === 'assistant' && isTyping && message === messages[messages.length - 1],
    variant: message.role === 'user' ? 'shadow' : 'filled',
    shape: 'round',
  }));

  return (
    <div className="chat-container">
      {/* 头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="chat-header"
      >
        <Space align="center">
          <Avatar
            size={48}
            icon={<RobotOutlined />}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: '2px solid rgba(255, 255, 255, 0.2)'
            }}
          />
          <div>
            <Title level={3} style={{ margin: 0, color: '#fff', fontWeight: 600 }}>
              AutoGen Assistant
            </Title>
            <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '14px' }}>
              🤖 智能对话助手 · 随时为您服务
            </Text>
          </div>
        </Space>
        <Button
          icon={<ClearOutlined />}
          onClick={handleClear}
          type="text"
          size="large"
          style={{
            color: '#fff',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)'
          }}
        >
          清除对话
        </Button>
      </motion.div>

      {/* 聊天区域 */}
      <div className="chat-content">
        <AnimatePresence>
          {messages.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="welcome-container"
            >
              <Welcome
                icon={<Avatar size={80} icon={<RobotOutlined />} style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: '3px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
                }} />}
                title="欢迎使用 AutoGen Assistant"
                description="我是您的智能助手，可以帮您解答问题、提供建议或进行创意对话。选择下方的提示开始对话，或直接输入您的问题。"
                extra={
                  <Prompts
                    title="✨ 试试这些问题"
                    items={promptItems}
                    onItemClick={handlePromptClick}
                    styles={{
                      list: {
                        width: '100%',
                        maxWidth: '600px'
                      },
                      item: {
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        borderRadius: '12px',
                        backdropFilter: 'blur(10px)',
                        color: '#fff',
                        transition: 'all 0.3s ease'
                      }
                    }}
                  />
                }
                style={{
                  background: 'transparent',
                  color: '#fff',
                  textAlign: 'center'
                }}
              />
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="messages-wrapper"
            >
              <Bubble.List
                items={bubbleItems}
                roles={{
                  user: {
                    placement: 'end',
                    variant: 'shadow',
                    shape: 'round',
                    styles: {
                      content: {
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: '#fff',
                        borderRadius: '18px 18px 4px 18px',
                        boxShadow: '0 4px 20px rgba(102, 126, 234, 0.3)'
                      }
                    }
                  },
                  assistant: {
                    placement: 'start',
                    variant: 'filled',
                    shape: 'round',
                    styles: {
                      content: {
                        background: 'rgba(255, 255, 255, 0.1)',
                        color: '#fff',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        borderRadius: '18px 18px 18px 4px',
                        backdropFilter: 'blur(10px)'
                      }
                    }
                  }
                }}
                autoScroll
                style={{
                  background: 'transparent',
                  padding: '20px',
                  height: '100%'
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="chat-input"
      >
        <Sender
          placeholder="输入您的消息..."
          onSubmit={handleSend}
          loading={isTyping}
          disabled={isTyping}
          style={{
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            backdropFilter: 'blur(10px)'
          }}
          styles={{
            input: {
              background: 'transparent',
              color: '#fff',
              border: 'none'
            },
            button: {
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '12px'
            }
          }}
        />
      </motion.div>
    </div>
  );
};

export default ChatInterface;
