import React, { useState, useRef, useEffect } from 'react';
import { Layout, Button, Space, Typography, Avatar, theme, Input, Card } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, ClearOutlined, SparklesOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useChatService } from '../hooks/useChatService';
import { Message } from '../types/chat';
import './ChatInterface.css';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;
const { TextArea } = Input;

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageIdCounter = useRef(0);
  const { token } = theme.useToken();

  const { sendMessage, clearConversation } = useChatService();

  // 预设提示词
  const promptItems = [
    {
      key: '1',
      label: '写一首诗',
      description: '创作一首优美的诗歌',
      icon: <SparklesOutlined />,
    },
    {
      key: '2',
      label: '解释量子计算',
      description: '用简单的语言解释复杂概念',
      icon: <SparklesOutlined />,
    },
    {
      key: '3',
      label: '推荐一本书',
      description: '根据兴趣推荐好书',
      icon: <SparklesOutlined />,
    },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 节流滚动，避免频繁滚动导致抖动
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const throttledScrollToBottom = () => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    scrollTimeoutRef.current = setTimeout(() => {
      scrollToBottom();
    }, 100);
  };

  useEffect(() => {
    throttledScrollToBottom();
  }, [messages, isTyping]);

  const handleSend = async (messageText?: string) => {
    const textToSend = messageText || inputValue;
    if (!textToSend.trim()) return;

    // 生成唯一ID
    const userMessageId = `user_${++messageIdCounter.current}_${Date.now()}`;
    const assistantMessageId = `assistant_${++messageIdCounter.current}_${Date.now()}`;

    const userMessage: Message = {
      id: userMessageId,
      content: textToSend,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      const assistantMessage: Message = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 使用 ref 来跟踪累积的内容，避免闭包问题
      let accumulatedContent = '';

      await sendMessage(textToSend, (chunk: string) => {
        accumulatedContent += chunk;
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages.find(msg => msg.id === assistantMessageId);
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content = accumulatedContent; // 直接设置而不是累加
          }
          return newMessages;
        });
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages.find(msg => msg.id === assistantMessageId);
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = '抱歉，发送消息时出现了错误，请稍后重试。';
          lastMessage.error = true;
        }
        return newMessages;
      });
    } finally {
      setIsTyping(false);
    }
  };

  const handleClear = async () => {
    try {
      await clearConversation();
      setMessages([]);
    } catch (error) {
      console.error('清除对话失败:', error);
    }
  };

  const handlePromptClick = (question: string) => {
    handleSend(question);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Layout className="chat-layout">
      {/* 头部 */}
      <Header className="chat-header">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="header-content"
        >
          <Space align="center">
            <Avatar
              size={48}
              icon={<RobotOutlined />}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: '2px solid rgba(255, 255, 255, 0.2)'
              }}
            />
            <div>
              <Title level={3} style={{ margin: 0, color: '#fff', fontWeight: 600 }}>
                AutoGen Assistant
              </Title>
              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '14px' }}>
                🤖 智能对话助手 · 随时为您服务
              </Text>
            </div>
          </Space>
          <Button
            icon={<ClearOutlined />}
            onClick={handleClear}
            type="text"
            size="large"
            className="clear-button"
          >
            清除对话
          </Button>
        </motion.div>
      </Header>

      {/* 聊天内容区域 */}
      <Content className="chat-content">
        <div className="messages-container">
          <AnimatePresence>
            {messages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="welcome-message"
              >
                <div className="welcome-content">
                  <Avatar
                    size={80}
                    icon={<RobotOutlined />}
                    className="welcome-avatar"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: '3px solid rgba(255, 255, 255, 0.2)',
                      boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
                    }}
                  />
                  <Title level={2} style={{ color: '#fff', marginTop: 24, fontWeight: 600 }}>
                    欢迎使用 AutoGen Assistant
                  </Title>
                  <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '16px' }}>
                    我是您的智能助手，可以帮您解答问题、提供建议或进行创意对话
                  </Text>
                  <div className="example-questions">
                    <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '14px' }}>
                      ✨ 试试问我：
                    </Text>
                    <div className="question-tags">
                      <span
                        className="question-tag"
                        onClick={() => handlePromptClick('写一首诗')}
                      >
                        <SparklesOutlined /> 写一首诗
                      </span>
                      <span
                        className="question-tag"
                        onClick={() => handlePromptClick('解释量子计算')}
                      >
                        <SparklesOutlined /> 解释量子计算
                      </span>
                      <span
                        className="question-tag"
                        onClick={() => handlePromptClick('推荐一本书')}
                      >
                        <SparklesOutlined /> 推荐一本书
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ) : (
              <div className="messages-list">
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`message-item ${message.role}`}
                  >
                    <div className="message-avatar">
                      <Avatar
                        icon={message.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                        style={{
                          background: message.role === 'user'
                            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                            : 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)'
                        }}
                      />
                    </div>
                    <Card className="message-bubble">
                      <div className="message-content">
                        {message.content}
                      </div>
                    </Card>
                  </motion.div>
                ))}
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="message-item assistant"
                  >
                    <div className="message-avatar">
                      <Avatar
                        icon={<RobotOutlined />}
                        style={{
                          background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)'
                        }}
                      />
                    </div>
                    <Card className="message-bubble typing">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </Card>
                  </motion.div>
                )}
              </div>
            )}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </div>
      </Content>

      {/* 输入区域 */}
      <Footer className="chat-footer">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="input-container"
        >
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的消息..."
            autoSize={{ minRows: 2, maxRows: 6 }}
            className="message-input"
            disabled={isTyping}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={() => handleSend()}
            disabled={!inputValue.trim() || isTyping}
            className="send-button"
            size="large"
          />
        </motion.div>
      </Footer>
    </Layout>
  );
};

export default ChatInterface;
