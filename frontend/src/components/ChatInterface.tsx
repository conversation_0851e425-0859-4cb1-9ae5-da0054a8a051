import React, { useState, useRef, useEffect } from 'react';
import { Layout, Input, Button, Space, Typography, Card, Avatar, Divider } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, ClearOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import MessageList from './MessageList';
import TypingIndicator from './TypingIndicator';
import { useChatService } from '../hooks/useChatService';
import { Message } from '../types/chat';
import './ChatInterface.css';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;
const { TextArea } = Input;

const ChatInterface: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { sendMessage, clearConversation } = useChatService();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const handleSend = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue; // 保存当前输入值
    setInputValue('');
    setIsTyping(true);

    try {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: '',
        role: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 使用 ref 来跟踪累积的内容，避免闭包问题
      let accumulatedContent = '';

      await sendMessage(currentInput, (chunk: string) => {
        accumulatedContent += chunk;
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage.role === 'assistant') {
            lastMessage.content = accumulatedContent; // 直接设置而不是累加
          }
          return newMessages;
        });
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage.role === 'assistant') {
          lastMessage.content = '抱歉，发送消息时出现了错误，请稍后重试。';
          lastMessage.error = true;
        }
        return newMessages;
      });
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleClear = async () => {
    try {
      await clearConversation();
      setMessages([]);
    } catch (error) {
      console.error('清除对话失败:', error);
    }
  };

  return (
    <Layout className="chat-layout">
      <Header className="chat-header glass-effect">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="header-content"
        >
          <Space align="center">
            <Avatar 
              size={40} 
              icon={<RobotOutlined />} 
              className="header-avatar"
            />
            <div>
              <Title level={4} style={{ margin: 0, color: '#fff' }}>
                AutoGen Assistant
              </Title>
              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>
                智能对话助手
              </Text>
            </div>
          </Space>
          <Button
            icon={<ClearOutlined />}
            onClick={handleClear}
            className="clear-button"
            type="text"
            style={{ color: '#fff' }}
          >
            清除对话
          </Button>
        </motion.div>
      </Header>

      <Content className="chat-content">
        <div className="messages-container glass-effect">
          <AnimatePresence>
            {messages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="welcome-message"
              >
                <div className="welcome-content">
                  <Avatar size={64} icon={<RobotOutlined />} className="welcome-avatar" />
                  <Title level={3} style={{ color: '#fff', marginTop: 16 }}>
                    欢迎使用 AutoGen Assistant
                  </Title>
                  <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    我是您的智能助手，可以帮您解答问题、提供建议或进行创意对话
                  </Text>
                  <div className="example-questions">
                    <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '14px' }}>
                      试试问我：
                    </Text>
                    <div className="question-tags">
                      <span className="question-tag">写一首诗</span>
                      <span className="question-tag">解释量子计算</span>
                      <span className="question-tag">推荐一本书</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ) : (
              <MessageList messages={messages} />
            )}
          </AnimatePresence>
          
          {isTyping && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>
      </Content>

      <Footer className="chat-footer">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="input-container glass-effect"
        >
          <TextArea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的消息..."
            autoSize={{ minRows: 2, maxRows: 6 }}
            className="message-input"
            disabled={isTyping}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            disabled={!inputValue.trim() || isTyping}
            className="send-button"
            size="large"
          />
        </motion.div>
      </Footer>
    </Layout>
  );
};

export default ChatInterface;
