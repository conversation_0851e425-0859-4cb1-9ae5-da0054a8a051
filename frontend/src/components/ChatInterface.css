/* 主容器 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  position: relative;
  overflow: hidden;
}

/* 头部样式 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  z-index: 10;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 欢迎页面容器 */
.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
  text-align: center;
}

/* 消息列表容器 */
.messages-wrapper {
  height: 100%;
  overflow: hidden;
}

/* 输入区域 */
.chat-input {
  padding: 20px 32px 32px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

/* 自定义 Ant Design X 组件样式 */
.ant-bubble {
  margin-bottom: 16px;
}

.ant-bubble-content {
  max-width: 70%;
  word-wrap: break-word;
  line-height: 1.6;
}

.ant-bubble-avatar {
  margin-bottom: auto;
}

/* 欢迎页面的提示卡片样式 */
.ant-prompts-item:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

/* 发送按钮悬停效果 */
.ant-sender-button:hover {
  background: linear-gradient(135deg, #7c8ef0 0%, #8b5fbf 100%) !important;
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

/* 输入框聚焦效果 */
.ant-sender-input:focus {
  border-color: rgba(102, 126, 234, 0.6) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 滚动条样式 */
.ant-bubble-list::-webkit-scrollbar {
  width: 6px;
}

.ant-bubble-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.ant-bubble-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.ant-bubble-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 加载状态动画 */
.ant-bubble-loading .ant-bubble-content {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chat-input {
    padding: 16px 20px 24px;
  }

  .welcome-container {
    padding: 20px;
  }

  .ant-bubble-content {
    max-width: 85%;
  }

  .messages-wrapper {
    padding: 0 10px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 12px 16px;
  }

  .chat-input {
    padding: 12px 16px 20px;
  }

  .welcome-container {
    padding: 16px;
  }

  .ant-bubble-content {
    max-width: 90%;
  }
}

/* 深色主题优化 */
.ant-bubble-content code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
}

.ant-bubble-content pre {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  padding: 12px !important;
}

.ant-bubble-content pre code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}
