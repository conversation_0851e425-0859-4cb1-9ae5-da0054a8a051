/* 主布局 */
.chat-layout {
  height: 100vh;
  background: transparent;
}

/* 头部样式 */
.chat-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 20px 32px;
  height: auto;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.clear-button {
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  borderRadius: 12px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdropFilter: blur(10px) !important;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px);
}

/* 聊天内容区域 */
.chat-content {
  background: transparent;
  padding: 0;
  overflow: hidden;
}

.messages-container {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 欢迎页面 */
.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 500px;
}

.example-questions {
  margin-top: 32px;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.question-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

.question-tag:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 80%;
  animation: fadeInUp 0.3s ease-out;
}

.message-item.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.assistant {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.message-bubble {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 18px !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.message-item.user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 18px 18px 4px 18px !important;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.message-content {
  color: #fff;
  line-height: 1.6;
  word-wrap: break-word;
}

.message-bubble.typing {
  padding: 16px 20px;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-footer {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 20px 32px;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  color: #fff !important;
  resize: none;
  min-height: 48px;
  padding: 12px 16px !important;
  line-height: 1.5;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.message-input:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(102, 126, 234, 0.6) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 16px !important;
  height: 48px !important;
  width: 48px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c8ef0 0%, #8b5fbf 100%) !important;
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed;
  transform: none !important;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 代码块样式 */
.message-content code {
  background: rgba(0, 0, 0, 0.3) !important;
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
}

.message-content pre {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
  overflow-x: auto;
}

.message-content pre code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chat-footer {
    padding: 16px 20px 24px;
  }

  .messages-container {
    padding: 16px;
  }

  .welcome-content {
    max-width: 400px;
  }

  .message-item {
    max-width: 90%;
  }

  .question-tags {
    flex-direction: column;
    align-items: center;
  }

  .input-container {
    gap: 8px;
  }

  .send-button {
    height: 44px !important;
    width: 44px !important;
  }

  .message-input {
    min-height: 44px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 12px 16px;
  }

  .chat-footer {
    padding: 12px 16px 20px;
  }

  .messages-container {
    padding: 12px;
  }

  .welcome-content {
    max-width: 300px;
  }

  .message-item {
    max-width: 95%;
  }

  .message-bubble {
    border-radius: 12px !important;
  }

  .message-item.user .message-bubble {
    border-radius: 12px 12px 4px 12px !important;
  }
}
