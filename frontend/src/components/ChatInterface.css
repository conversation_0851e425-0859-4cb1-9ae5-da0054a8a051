.chat-layout {
  height: 100vh;
  background: transparent;
}

.chat-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  height: auto;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-avatar {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.clear-button {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.chat-content {
  padding: 24px;
  background: transparent;
  overflow: hidden;
}

.messages-container {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 400px;
}

.welcome-avatar {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.example-questions {
  margin-top: 24px;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 12px;
}

.question-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.question-tag:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.chat-footer {
  background: transparent;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #fff;
  resize: none;
  min-height: 44px;
  padding: 12px 16px;
  line-height: 1.5;
  font-size: 14px;
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.message-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.send-button {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  height: 44px;
  width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chat-content {
    padding: 16px;
  }
  
  .messages-container {
    padding: 16px;
  }
  
  .chat-footer {
    padding: 12px 16px;
  }
  
  .input-container {
    padding: 12px;
  }
  
  .welcome-content {
    max-width: 300px;
  }
  
  .question-tags {
    flex-direction: column;
    align-items: center;
  }
}
