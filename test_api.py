#!/usr/bin/env python3
"""
测试后端API的脚本
"""
import asyncio
import aiohttp
import json
import sys
from pathlib import Path

async def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

async def test_non_stream_chat():
    """测试非流式聊天接口"""
    print("🔍 测试非流式聊天接口...")
    try:
        async with aiohttp.ClientSession() as session:
            payload = {
                "message": "你好，请简单介绍一下你自己",
                "conversation_id": "test"
            }
            async with session.post(
                'http://localhost:8000/chat',
                json=payload,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 非流式聊天成功:")
                    print(f"   内容: {data.get('content', '')[:100]}...")
                    print(f"   完成: {data.get('is_complete', False)}")
                    return True
                else:
                    text = await response.text()
                    print(f"❌ 非流式聊天失败: {response.status} - {text}")
                    return False
    except Exception as e:
        print(f"❌ 非流式聊天异常: {e}")
        return False

async def test_stream_chat():
    """测试流式聊天接口"""
    print("🔍 测试流式聊天接口...")
    try:
        async with aiohttp.ClientSession() as session:
            payload = {
                "message": "写一首关于春天的短诗",
                "conversation_id": "test_stream"
            }
            async with session.post(
                'http://localhost:8000/chat/stream',
                json=payload,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    print("✅ 流式聊天开始，接收数据:")
                    content = ""
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                if data.get('content'):
                                    content += data['content']
                                    print(data['content'], end='', flush=True)
                                if data.get('is_complete'):
                                    print(f"\n✅ 流式聊天完成，总长度: {len(content)}")
                                    return True
                            except json.JSONDecodeError:
                                continue
                    return True
                else:
                    text = await response.text()
                    print(f"❌ 流式聊天失败: {response.status} - {text}")
                    return False
    except Exception as e:
        print(f"❌ 流式聊天异常: {e}")
        return False

async def test_clear_conversation():
    """测试清除对话接口"""
    print("🔍 测试清除对话接口...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.delete('http://localhost:8000/chat/test') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 清除对话成功: {data}")
                    return True
                else:
                    text = await response.text()
                    print(f"❌ 清除对话失败: {response.status} - {text}")
                    return False
    except Exception as e:
        print(f"❌ 清除对话异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 AutoGen Chat API 测试")
    print("=" * 50)
    
    # 检查后端是否运行
    print("🔍 检查后端服务是否运行...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 后端服务运行中: {data}")
                else:
                    print("❌ 后端服务未响应")
                    print("💡 请先运行: python start_backend.py")
                    return 1
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        print("💡 请先运行: python start_backend.py")
        return 1
    
    print("\n" + "-" * 50)
    
    # 运行测试
    tests = [
        ("健康检查", test_health_check),
        ("非流式聊天", test_non_stream_chat),
        ("流式聊天", test_stream_chat),
        ("清除对话", test_clear_conversation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if await test_func():
                passed += 1
            else:
                print(f"❌ 测试失败: {test_name}")
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {e}")
        
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查后端服务")
        return 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
