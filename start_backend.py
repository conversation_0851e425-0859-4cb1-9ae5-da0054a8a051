#!/usr/bin/env python3
"""
启动后端服务的脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    # 获取项目根目录
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # 检查backend目录是否存在
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return 1
    
    # 切换到backend目录
    os.chdir(backend_dir)
    
    print("🚀 启动AutoGen聊天后端服务...")
    print(f"📁 工作目录: {backend_dir}")
    
    # 检查是否有虚拟环境
    venv_python = None
    if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
        venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
    elif (backend_dir / "venv" / "bin" / "python").exists():
        venv_python = str(backend_dir / "venv" / "bin" / "python")
    
    # 选择Python解释器
    python_cmd = venv_python if venv_python else sys.executable
    
    print(f"🐍 使用Python: {python_cmd}")
    
    # 检查依赖是否安装
    try:
        result = subprocess.run([python_cmd, "-c", "import fastapi, autogen_agentchat"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("📦 正在安装依赖...")
            subprocess.run([python_cmd, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 安装依赖失败")
        return 1
    
    # 启动服务
    try:
        print("🌟 启动FastAPI服务器...")
        print("📡 服务地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🔄 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        subprocess.run([python_cmd, "run.py"])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动服务失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
