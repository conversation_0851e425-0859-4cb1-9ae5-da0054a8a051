#!/usr/bin/env python3
"""
启动前端服务的脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    # 获取项目根目录
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    # 检查frontend目录是否存在
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return 1
    
    # 切换到frontend目录
    os.chdir(frontend_dir)
    
    print("🚀 启动AutoGen聊天前端服务...")
    print(f"📁 工作目录: {frontend_dir}")
    
    # 检查Node.js是否安装
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"📦 Node.js版本: {result.stdout.strip()}")
        else:
            print("❌ Node.js未安装，请先安装Node.js")
            return 1
    except FileNotFoundError:
        print("❌ Node.js未安装，请先安装Node.js")
        return 1
    
    # 检查npm是否可用
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"📦 npm版本: {result.stdout.strip()}")
        else:
            print("❌ npm不可用")
            return 1
    except FileNotFoundError:
        print("❌ npm不可用")
        return 1
    
    # 检查package.json是否存在
    if not (frontend_dir / "package.json").exists():
        print("❌ package.json不存在")
        return 1
    
    # 检查node_modules是否存在，如果不存在则安装依赖
    if not (frontend_dir / "node_modules").exists():
        print("📦 正在安装依赖...")
        try:
            subprocess.run(["npm", "install"], check=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return 1
    
    # 启动开发服务器
    try:
        print("🌟 启动Vite开发服务器...")
        print("🌐 前端地址: http://localhost:3000")
        print("🔄 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        subprocess.run(["npm", "run", "dev"])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动服务失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
