#!/usr/bin/env python3
"""
验证reload警告修复的脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def verify_backend_fix():
    """验证后端启动修复"""
    print("🔍 验证后端启动修复...")
    
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 检查run.py是否存在
    run_py = backend_dir / "run.py"
    if not run_py.exists():
        print("❌ backend/run.py文件不存在")
        return False
    
    print("✅ backend/run.py文件存在")
    
    # 检查run.py内容
    try:
        with open(run_py, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'uvicorn.run' in content and '"main:app"' in content:
                print("✅ run.py内容正确")
            else:
                print("❌ run.py内容不正确")
                return False
    except Exception as e:
        print(f"❌ 读取run.py失败: {e}")
        return False
    
    return True

def verify_start_scripts():
    """验证启动脚本修复"""
    print("🔍 验证启动脚本修复...")
    
    scripts = [
        ("start_backend.py", "python run.py"),
        ("start_app.py", "python run.py")
    ]
    
    for script_name, expected_content in scripts:
        script_path = Path(__file__).parent / script_name
        if not script_path.exists():
            print(f"❌ {script_name}不存在")
            return False
        
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if expected_content in content:
                    print(f"✅ {script_name}已更新")
                else:
                    print(f"❌ {script_name}未正确更新")
                    return False
        except Exception as e:
            print(f"❌ 读取{script_name}失败: {e}")
            return False
    
    return True

def test_dry_run():
    """测试启动（不实际启动服务）"""
    print("🔍 测试启动脚本语法...")
    
    backend_dir = Path(__file__).parent / "backend"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查Python语法
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "run.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ run.py语法正确")
            return True
        else:
            print(f"❌ run.py语法错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主验证函数"""
    print("🧪 验证reload警告修复")
    print("=" * 50)
    
    checks = [
        ("后端文件检查", verify_backend_fix),
        ("启动脚本检查", verify_start_scripts),
        ("语法检查", test_dry_run),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}:")
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name}通过")
            else:
                print(f"❌ {check_name}失败")
        except Exception as e:
            print(f"❌ {check_name}异常: {e}")
        
        print("-" * 30)
    
    print(f"\n📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有检查通过！reload警告已修复。")
        print("\n📋 现在可以运行:")
        print("1. python start_backend.py  # 无警告启动后端")
        print("2. python start_app.py      # 无警告启动完整应用")
        print("3. cd backend && python run.py  # 直接启动")
        return 0
    else:
        print("⚠️ 部分检查失败，请检查修复。")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 验证被中断")
        sys.exit(1)
