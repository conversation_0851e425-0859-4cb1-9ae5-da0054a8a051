#!/usr/bin/env python3
"""
同时启动前端和后端服务的脚本
"""
import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

class ServiceManager:
    def __init__(self):
        self.processes = []
        self.project_root = Path(__file__).parent
        
    def start_backend(self):
        """启动后端服务"""
        backend_dir = self.project_root / "backend"
        if not backend_dir.exists():
            print("❌ backend目录不存在")
            return None
            
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print("🚀 启动后端服务...")
        try:
            # 检查并安装依赖
            result = subprocess.run([python_cmd, "-c", "import fastapi, autogen_agentchat"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("📦 安装后端依赖...")
                subprocess.run([python_cmd, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            
            # 启动后端
            process = subprocess.Popen([
                python_cmd, "run.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('backend', process))
            print("✅ 后端服务启动成功 (http://localhost:8000)")
            return process
            
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return None
    
    def start_frontend(self):
        """启动前端服务"""
        frontend_dir = self.project_root / "frontend"
        if not frontend_dir.exists():
            print("❌ frontend目录不存在")
            return None
            
        os.chdir(frontend_dir)
        
        print("🚀 启动前端服务...")
        try:
            # 检查Node.js
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Node.js未安装")
                return None
            
            # 安装依赖
            if not (frontend_dir / "node_modules").exists():
                print("📦 安装前端依赖...")
                subprocess.run(["npm", "install"], check=True)
            
            # 启动前端
            process = subprocess.Popen([
                "npm", "run", "dev"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('frontend', process))
            print("✅ 前端服务启动成功 (http://localhost:3000)")
            return process
            
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return None
    
    def monitor_process(self, name, process):
        """监控进程输出"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"[{name}] {line.strip()}")
        except Exception:
            pass
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        for name, process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {name}服务已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 强制停止{name}服务")
            except Exception as e:
                print(f"❌ 停止{name}服务失败: {e}")
    
    def run(self):
        """运行应用"""
        print("🌟 AutoGen Chat Application")
        print("=" * 50)
        
        # 设置信号处理
        def signal_handler(signum, frame):
            self.stop_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动后端
        backend_process = self.start_backend()
        if not backend_process:
            print("❌ 无法启动后端服务")
            return 1
        
        # 等待后端启动
        print("⏳ 等待后端服务启动...")
        time.sleep(3)
        
        # 启动前端
        frontend_process = self.start_frontend()
        if not frontend_process:
            print("❌ 无法启动前端服务")
            self.stop_all()
            return 1
        
        # 等待前端启动
        print("⏳ 等待前端服务启动...")
        time.sleep(3)
        
        print("\n" + "=" * 50)
        print("🎉 所有服务启动成功！")
        print("🌐 前端地址: http://localhost:3000")
        print("📡 后端地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🔄 按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        # 启动监控线程
        backend_monitor = threading.Thread(
            target=self.monitor_process, 
            args=('backend', backend_process),
            daemon=True
        )
        frontend_monitor = threading.Thread(
            target=self.monitor_process, 
            args=('frontend', frontend_process),
            daemon=True
        )
        
        backend_monitor.start()
        frontend_monitor.start()
        
        # 等待进程结束
        try:
            while True:
                # 检查进程是否还在运行
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️ {name}服务意外停止")
                        self.stop_all()
                        return 1
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        self.stop_all()
        return 0

def main():
    manager = ServiceManager()
    return manager.run()

if __name__ == "__main__":
    sys.exit(main())
