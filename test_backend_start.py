#!/usr/bin/env python3
"""
测试后端启动的脚本（不会出现reload警告）
"""
import os
import sys
import time
import subprocess
import signal
from pathlib import Path

def test_backend_start():
    """测试后端启动"""
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 切换到backend目录
    original_cwd = os.getcwd()
    os.chdir(backend_dir)
    
    try:
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print("🚀 测试后端启动（无reload警告）...")
        print(f"📁 工作目录: {backend_dir}")
        print(f"🐍 使用Python: {python_cmd}")
        
        # 启动后端服务
        print("🌟 启动FastAPI服务器...")
        process = subprocess.Popen([python_cmd, "run.py"])
        
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查服务是否正常运行
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务启动成功，无警告！")
                print("📡 服务地址: http://localhost:8000")
                print("📚 API文档: http://localhost:8000/docs")
                success = True
            else:
                print(f"❌ 服务响应异常: {response.status_code}")
                success = False
        except ImportError:
            print("⚠️ requests模块未安装，无法测试HTTP请求")
            print("💡 可以手动访问 http://localhost:8000/health 测试")
            success = True
        except Exception as e:
            print(f"❌ 服务测试失败: {e}")
            success = False
        
        # 停止服务
        print("\n🛑 停止测试服务...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        
        return success
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主函数"""
    print("🧪 后端启动测试")
    print("=" * 50)
    
    if test_backend_start():
        print("\n🎉 测试通过！后端可以正常启动且无警告。")
        print("\n📋 下一步:")
        print("1. 运行完整应用: python start_app.py")
        print("2. 或单独启动后端: python start_backend.py")
        return 0
    else:
        print("\n❌ 测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
