#!/usr/bin/env python3
"""
最终测试reload警告修复的脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def test_backend_startup():
    """测试后端启动是否有警告"""
    print("🧪 最终测试 - 后端启动警告检查")
    print("=" * 50)
    
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print(f"🐍 Python解释器: {python_cmd}")
        print(f"📁 工作目录: {backend_dir}")
        
        # 测试方法1: 直接运行run.py
        print("\n📋 测试1: 运行 run.py")
        print("⏳ 启动服务（5秒后自动停止）...")
        
        process = subprocess.Popen(
            [python_cmd, "run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 等待5秒并收集输出
        time.sleep(5)
        
        # 停止进程
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        # 分析输出
        warning_found = False
        warning_text = "You must pass the application as an import string"
        
        print("\n📝 输出分析:")
        
        if stdout:
            print("📤 标准输出:")
            for line in stdout.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    if warning_text in line:
                        warning_found = True
        
        if stderr:
            print("📤 错误输出:")
            for line in stderr.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    if warning_text in line:
                        warning_found = True
        
        if warning_found:
            print("❌ 发现reload警告！")
            return False
        else:
            print("✅ 没有发现reload警告")
        
        # 测试方法2: 直接运行main.py
        print("\n📋 测试2: 直接运行 main.py")
        print("⏳ 启动服务（5秒后自动停止）...")
        
        process2 = subprocess.Popen(
            [python_cmd, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 等待5秒
        time.sleep(5)
        
        # 停止进程
        process2.terminate()
        try:
            stdout2, stderr2 = process2.communicate(timeout=3)
        except subprocess.TimeoutExpired:
            process2.kill()
            stdout2, stderr2 = process2.communicate()
        
        # 分析输出
        warning_found2 = False
        
        print("\n📝 输出分析:")
        
        if stdout2:
            print("📤 标准输出:")
            for line in stdout2.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    if warning_text in line:
                        warning_found2 = True
        
        if stderr2:
            print("📤 错误输出:")
            for line in stderr2.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    if warning_text in line:
                        warning_found2 = True
        
        if warning_found2:
            print("❌ 发现reload警告！")
            return False
        else:
            print("✅ 没有发现reload警告")
        
        # 最终结果
        if not warning_found and not warning_found2:
            print("\n🎉 所有测试通过！reload警告已完全修复。")
            return True
        else:
            print("\n❌ 仍然存在警告问题。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def check_file_contents():
    """检查关键文件内容"""
    print("\n🔍 检查关键文件内容:")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # 检查main.py
    main_file = backend_dir / "main.py"
    if main_file.exists():
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'uvicorn.run(app,' in content:
                print("❌ main.py: 仍使用 uvicorn.run(app, ...)")
                return False
            elif 'uvicorn.run("main:app"' in content:
                print("✅ main.py: 使用正确的字符串格式")
            else:
                print("ℹ️ main.py: 没有找到uvicorn.run调用")
        except Exception as e:
            print(f"❌ 读取main.py失败: {e}")
            return False
    
    # 检查run.py
    run_file = backend_dir / "run.py"
    if run_file.exists():
        try:
            with open(run_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '"main:app"' in content:
                print("✅ run.py: 使用正确的字符串格式")
            else:
                print("❌ run.py: 格式可能有问题")
                return False
        except Exception as e:
            print(f"❌ 读取run.py失败: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("🔧 AutoGen Chat - Reload警告修复验证")
    print("=" * 60)
    
    # 检查文件内容
    if not check_file_contents():
        print("\n❌ 文件内容检查失败")
        return 1
    
    # 测试启动
    if test_backend_startup():
        print("\n🎉 修复验证成功！")
        print("\n📋 现在可以安全使用以下命令启动服务:")
        print("1. python start_backend.py")
        print("2. python start_app.py")
        print("3. cd backend && python run.py")
        print("4. cd backend && python main.py")
        print("\n所有方式都不会出现reload警告。")
        return 0
    else:
        print("\n❌ 修复验证失败")
        print("\n💡 可能的解决方案:")
        print("1. 确保所有uvicorn.run调用都使用字符串格式")
        print("2. 检查是否有其他地方导入了有问题的代码")
        print("3. 重新安装uvicorn: pip install --upgrade uvicorn")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
