#!/usr/bin/env python3
"""
测试是否还有reload警告的脚本
"""
import os
import sys
import subprocess
import time
import signal
from pathlib import Path

def test_main_py_direct():
    """测试直接运行main.py"""
    print("🔍 测试直接运行main.py...")
    
    backend_dir = Path(__file__).parent / "backend"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print(f"🐍 使用Python: {python_cmd}")
        print("⏳ 启动main.py（5秒后自动停止）...")
        
        # 启动进程并捕获输出
        process = subprocess.Popen(
            [python_cmd, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待5秒
        time.sleep(5)
        
        # 停止进程
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        # 检查输出中是否有警告
        warning_found = False
        if "You must pass the application as an import string" in stderr:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stderr)
        elif "You must pass the application as an import string" in stdout:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stdout)
        else:
            print("✅ 没有发现reload警告")
            if stderr:
                print("📝 stderr输出:")
                print(stderr)
            if stdout:
                print("📝 stdout输出:")
                print(stdout)
        
        return not warning_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def test_run_py():
    """测试运行run.py"""
    print("🔍 测试运行run.py...")
    
    backend_dir = Path(__file__).parent / "backend"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print(f"🐍 使用Python: {python_cmd}")
        print("⏳ 启动run.py（5秒后自动停止）...")
        
        # 启动进程并捕获输出
        process = subprocess.Popen(
            [python_cmd, "run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待5秒
        time.sleep(5)
        
        # 停止进程
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        # 检查输出中是否有警告
        warning_found = False
        if "You must pass the application as an import string" in stderr:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stderr)
        elif "You must pass the application as an import string" in stdout:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stdout)
        else:
            print("✅ 没有发现reload警告")
            if stderr:
                print("📝 stderr输出:")
                print(stderr)
            if stdout:
                print("📝 stdout输出:")
                print(stdout)
        
        return not warning_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def test_uvicorn_command():
    """测试uvicorn命令行启动"""
    print("🔍 测试uvicorn命令行启动...")
    
    backend_dir = Path(__file__).parent / "backend"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        
        # 检查虚拟环境
        venv_python = None
        if (backend_dir / "venv" / "Scripts" / "python.exe").exists():
            venv_python = str(backend_dir / "venv" / "Scripts" / "python.exe")
        elif (backend_dir / "venv" / "bin" / "python").exists():
            venv_python = str(backend_dir / "venv" / "bin" / "python")
        
        python_cmd = venv_python if venv_python else sys.executable
        
        print(f"🐍 使用Python: {python_cmd}")
        print("⏳ 启动uvicorn命令（5秒后自动停止）...")
        
        # 启动进程并捕获输出
        process = subprocess.Popen(
            [python_cmd, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待5秒
        time.sleep(5)
        
        # 停止进程
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        # 检查输出中是否有警告
        warning_found = False
        if "You must pass the application as an import string" in stderr:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stderr)
        elif "You must pass the application as an import string" in stdout:
            warning_found = True
            print("❌ 仍然有reload警告:")
            print(stdout)
        else:
            print("✅ 没有发现reload警告")
            if stderr:
                print("📝 stderr输出:")
                print(stderr)
            if stdout:
                print("📝 stdout输出:")
                print(stdout)
        
        return not warning_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主测试函数"""
    print("🧪 测试reload警告修复")
    print("=" * 50)
    
    tests = [
        ("直接运行main.py", test_main_py_direct),
        ("运行run.py", test_run_py),
        ("uvicorn命令行", test_uvicorn_command),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}通过")
            else:
                print(f"❌ {test_name}失败")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
        
        print("-" * 50)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！reload警告已完全修复。")
        return 0
    else:
        print("⚠️ 部分测试失败，可能仍有警告。")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
