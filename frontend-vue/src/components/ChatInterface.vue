<template>
  <div class="chat-container">
    <!-- 头部 -->
    <div class="chat-header glass-effect">
      <div class="header-content">
        <div class="header-left">
          <a-avatar 
            :size="48" 
            class="header-avatar"
          >
            <template #icon>
              <RobotOutlined />
            </template>
          </a-avatar>
          <div class="header-info">
            <h3 class="header-title">AutoGen Assistant</h3>
            <p class="header-subtitle">🤖 智能对话助手 · 随时为您服务</p>
          </div>
        </div>
        <a-button 
          type="text" 
          size="large" 
          class="clear-button"
          @click="handleClear"
        >
          <template #icon>
            <ClearOutlined />
          </template>
          清除对话
        </a-button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-content" ref="chatContentRef">
      <!-- 欢迎页面 -->
      <div v-if="messages.length === 0" class="welcome-container">
        <div class="welcome-content">
          <a-avatar 
            :size="80" 
            class="welcome-avatar"
          >
            <template #icon>
              <RobotOutlined />
            </template>
          </a-avatar>
          <h2 class="welcome-title">欢迎使用 AutoGen Assistant</h2>
          <p class="welcome-description">
            我是您的智能助手，可以帮您解答问题、提供建议或进行创意对话
          </p>
          <div class="example-questions">
            <p class="example-title">✨ 试试问我：</p>
            <div class="question-tags">
              <a-button 
                v-for="question in exampleQuestions" 
                :key="question"
                class="question-tag"
                @click="handleExampleClick(question)"
              >
                <template #icon>
                  <SparklesOutlined />
                </template>
                {{ question }}
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-else class="messages-list">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="['message-item', message.role]"
        >
          <a-avatar 
            :size="40"
            :class="['message-avatar', message.role]"
          >
            <template #icon>
              <UserOutlined v-if="message.role === 'user'" />
              <RobotOutlined v-else />
            </template>
          </a-avatar>
          <div class="message-bubble glass-effect">
            <div class="message-content">
              {{ message.content }}
            </div>
            <div class="message-time">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>

        <!-- 打字指示器 -->
        <div v-if="isTyping" class="message-item assistant">
          <a-avatar 
            :size="40"
            class="message-avatar assistant"
          >
            <template #icon>
              <RobotOutlined />
            </template>
          </a-avatar>
          <div class="message-bubble glass-effect typing">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-footer glass-effect">
      <div class="input-container">
        <a-textarea
          v-model:value="inputValue"
          :placeholder="isTyping ? '正在思考中...' : '输入您的消息...'"
          :auto-size="{ minRows: 2, maxRows: 6 }"
          :disabled="isTyping"
          class="message-input"
          @keydown="handleKeyDown"
        />
        <a-button 
          type="primary" 
          size="large"
          :disabled="!inputValue.trim() || isTyping"
          :loading="isTyping"
          class="send-button"
          @click="handleSend"
        >
          <template #icon>
            <SendOutlined />
          </template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { 
  RobotOutlined, 
  UserOutlined, 
  ClearOutlined, 
  SendOutlined,
  SparklesOutlined 
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useChatStore } from '../stores/chat'

const chatStore = useChatStore()
const { messages, isTyping, sendMessage, clearConversation } = chatStore

const inputValue = ref('')
const chatContentRef = ref<HTMLElement>()

const exampleQuestions = [
  '写一首诗',
  '解释量子计算', 
  '推荐一本书'
]

// 处理发送消息
const handleSend = async () => {
  if (!inputValue.value.trim() || isTyping) return
  
  const messageText = inputValue.value.trim()
  inputValue.value = ''
  
  try {
    await sendMessage(messageText)
    await scrollToBottom()
  } catch (error) {
    message.error('发送消息失败，请稍后重试')
  }
}

// 处理示例问题点击
const handleExampleClick = async (question: string) => {
  inputValue.value = question
  await handleSend()
}

// 处理清除对话
const handleClear = async () => {
  try {
    await clearConversation()
    message.success('对话已清除')
  } catch (error) {
    message.error('清除对话失败')
  }
}

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    handleSend()
  }
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

// 格式化时间
const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* 头部样式 */
.chat-header {
  padding: 20px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.header-info {
  color: #fff;
}

.header-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.8;
}

.clear-button {
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.clear-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 欢迎页面 */
.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 500px;
  color: #fff;
}

.welcome-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.welcome-description {
  font-size: 16px;
  opacity: 0.8;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.example-questions {
  margin-top: 32px;
}

.example-title {
  font-size: 14px;
  opacity: 0.6;
  margin: 0 0 16px 0;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.question-tag {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.question-tag:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 80%;
  animation: fadeInUp 0.3s ease-out;
}

.message-item.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.assistant {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.message-avatar.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message-avatar.assistant {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.message-bubble {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 18px;
  padding: 12px 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.message-item.user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 18px 18px 4px 18px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.message-content {
  color: #fff;
  line-height: 1.6;
  word-wrap: break-word;
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  opacity: 0.6;
  color: #fff;
  text-align: right;
}

.message-item.assistant .message-time {
  text-align: left;
}

/* 打字指示器 */
.message-bubble.typing {
  padding: 16px 20px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-footer {
  padding: 20px 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  color: #fff !important;
  backdrop-filter: blur(10px);
}

.message-input:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(102, 126, 234, 0.6) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 16px !important;
  height: 48px !important;
  width: 48px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c8ef0 0%, #8b5fbf 100%) !important;
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chat-footer {
    padding: 16px 20px 24px;
  }

  .chat-content {
    padding: 16px;
  }

  .welcome-content {
    max-width: 400px;
  }

  .message-item {
    max-width: 90%;
  }

  .question-tags {
    flex-direction: column;
    align-items: center;
  }
}
</style>
