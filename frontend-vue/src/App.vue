<template>
  <div id="app">
    <!-- 背景装饰 -->
    <div class="app-background">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 简单聊天界面 -->
    <div class="simple-chat">
      <div class="chat-header">
        <h2>🤖 AutoGen Chat</h2>
        <button @click="clearMessages">清除</button>
      </div>

      <div class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="welcome">
          <h3>欢迎使用聊天助手</h3>
          <p>请输入您的问题开始对话</p>
          <div class="example-buttons">
            <button @click="sendExample('写一首诗')">写一首诗</button>
            <button @click="sendExample('解释量子计算')">解释量子计算</button>
            <button @click="sendExample('推荐一本书')">推荐一本书</button>
          </div>
        </div>

        <div v-for="msg in messages" :key="msg.id" :class="['message', msg.role]">
          <div class="avatar">{{ msg.role === 'user' ? '👤' : '🤖' }}</div>
          <div class="content">{{ msg.content }}</div>
        </div>

        <div v-if="isTyping" class="message assistant">
          <div class="avatar">🤖</div>
          <div class="content typing">正在思考...</div>
        </div>
      </div>

      <div class="chat-input">
        <textarea
          v-model="inputText"
          placeholder="输入您的消息..."
          @keydown.enter.prevent="sendMessage"
          :disabled="isTyping"
        ></textarea>
        <button @click="sendMessage" :disabled="!inputText.trim() || isTyping">
          发送
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

const messages = ref<Message[]>([])
const inputText = ref('')
const isTyping = ref(false)
const messagesContainer = ref<HTMLElement>()

const sendMessage = async () => {
  if (!inputText.value.trim() || isTyping.value) return

  const userMessage: Message = {
    id: Date.now().toString(),
    content: inputText.value.trim(),
    role: 'user',
    timestamp: new Date()
  }

  messages.value.push(userMessage)
  const messageText = inputText.value.trim()
  inputText.value = ''
  isTyping.value = true

  await scrollToBottom()

  // 发送到后端
  try {
    const response = await fetch('http://localhost:8000/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: messageText,
        conversation_id: 'default'
      }),
    })

    if (!response.ok) {
      throw new Error('网络错误')
    }

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: '',
      role: 'assistant',
      timestamp: new Date()
    }

    messages.value.push(assistantMessage)

    const reader = response.body?.getReader()
    if (!reader) throw new Error('无法读取响应')

    const decoder = new TextDecoder()
    let buffer = ''

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      buffer += decoder.decode(value, { stream: true })
      const lines = buffer.split('\n')
      buffer = lines.pop() || ''

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6))
            if (data.content) {
              assistantMessage.content += data.content
            }
          } catch (e) {
            console.warn('解析错误:', e)
          }
        }
      }

      await scrollToBottom()
    }
  } catch (error) {
    console.error('发送失败:', error)
    messages.value.push({
      id: (Date.now() + 2).toString(),
      content: '抱歉，发送消息时出现错误，请稍后重试。',
      role: 'assistant',
      timestamp: new Date()
    })
  } finally {
    isTyping.value = false
    await scrollToBottom()
  }
}

const sendExample = (text: string) => {
  inputText.value = text
  sendMessage()
}

const clearMessages = () => {
  messages.value = []
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}
</script>

<style scoped>
#app {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
}

.app-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -10;
}

.simple-chat {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-header {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h2 {
  margin: 0;
  color: white;
  font-size: 24px;
}

.chat-header button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
}

.chat-header button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome {
  text-align: center;
  color: white;
  margin-top: 50px;
}

.welcome h3 {
  font-size: 28px;
  margin-bottom: 16px;
}

.welcome p {
  font-size: 16px;
  opacity: 0.8;
  margin-bottom: 32px;
}

.example-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.example-buttons button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.example-buttons button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
  animation: fadeIn 0.3s ease;
}

.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message.assistant {
  align-self: flex-start;
}

.message .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.message .content {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 18px;
  padding: 12px 16px;
  color: white;
  line-height: 1.6;
  backdrop-filter: blur(10px);
}

.message.user .content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.content.typing {
  font-style: italic;
  opacity: 0.8;
}

.chat-input {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.chat-input textarea {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  color: white;
  padding: 12px 16px;
  resize: none;
  outline: none;
  min-height: 48px;
  font-family: inherit;
}

.chat-input textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input textarea:focus {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.chat-input button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 16px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.chat-input button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.chat-input button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-chat {
    margin: 0;
    border-radius: 0;
  }

  .example-buttons {
    flex-direction: column;
    align-items: center;
  }

  .message {
    max-width: 90%;
  }
}
</style>
