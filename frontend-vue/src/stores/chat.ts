import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  error?: boolean
}

interface ChatResponse {
  content: string
  is_complete: boolean
  error?: boolean
}

export const useChatStore = defineStore('chat', () => {
  const messages = ref<Message[]>([])
  const isTyping = ref(false)
  const messageIdCounter = ref(0)

  // API 基础配置
  const API_BASE_URL = 'http://localhost:8000'

  // 发送消息
  const sendMessage = async (messageText: string) => {
    if (!messageText.trim()) return

    // 生成唯一ID
    const userMessageId = `user_${++messageIdCounter.value}_${Date.now()}`
    const assistantMessageId = `assistant_${++messageIdCounter.value}_${Date.now()}`

    // 添加用户消息
    const userMessage: Message = {
      id: userMessageId,
      content: messageText,
      role: 'user',
      timestamp: new Date(),
    }

    messages.value.push(userMessage)
    isTyping.value = true

    try {
      // 添加助手消息占位符
      const assistantMessage: Message = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: new Date(),
      }

      messages.value.push(assistantMessage)

      // 发送流式请求
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          conversation_id: 'default'
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let accumulatedContent = ''

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        
        // 处理可能的多个SSE消息
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留最后一个可能不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: ChatResponse = JSON.parse(line.slice(6))
              
              if (data.error) {
                throw new Error(data.content)
              }
              
              if (data.content) {
                accumulatedContent += data.content
                
                // 更新助手消息内容
                const messageIndex = messages.value.findIndex(msg => msg.id === assistantMessageId)
                if (messageIndex !== -1) {
                  messages.value[messageIndex].content = accumulatedContent
                }
              }
              
              if (data.is_complete) {
                return
              }
            } catch (parseError) {
              console.warn('解析SSE数据失败:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 更新助手消息为错误状态
      const messageIndex = messages.value.findIndex(msg => msg.id === assistantMessageId)
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = '抱歉，发送消息时出现了错误，请稍后重试。'
        messages.value[messageIndex].error = true
      }
      
      throw error
    } finally {
      isTyping.value = false
    }
  }

  // 清除对话
  const clearConversation = async () => {
    try {
      await axios.delete(`${API_BASE_URL}/chat/default`)
      messages.value = []
    } catch (error) {
      console.error('清除对话失败:', error)
      throw error
    }
  }

  return {
    messages,
    isTyping,
    sendMessage,
    clearConversation
  }
})
